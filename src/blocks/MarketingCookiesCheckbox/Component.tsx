'use client'
import React, { useState, useEffect } from 'react'

export interface MarketingCookiesCheckboxProps {
  label?: string
  className?: string
}

export const MarketingCookiesCheckbox: React.FC<MarketingCookiesCheckboxProps> = ({
  label = "I agree to receive marketing communications",
  className
}) => {
  const [isChecked, setIsChecked] = useState(true)

  // Load saved preference on mount
  useEffect(() => {
    const savedPreference = localStorage.getItem('marketingCookiesConsent')
    if (savedPreference !== null) {
      setIsChecked(savedPreference === 'true')
    } else {
      // Default to checked and save the preference
      setIsChecked(true)
      localStorage.setItem('marketingCookiesConsent', 'true')
    }
  }, [])

  // Save preference when changed
  const handleChange = (checked: boolean) => {
    setIsChecked(checked)
    localStorage.setItem('marketingCookiesConsent', checked.toString())

    // Dispatch custom event for other parts of the app to listen to
    window.dispatchEvent(new CustomEvent('marketingCookiesChanged', {
      detail: { enabled: checked }
    }))
  }

  return (
    <div className={`inline-flex items-center gap-2 ${className || ''}`}>
      <input
        type="checkbox"
        id="marketing-cookies-checkbox"
        checked={isChecked}
        onChange={(e) => handleChange(e.target.checked)}
        className="w-4 h-4 text-navy bg-white border-2 border-navy rounded focus:ring-navy focus:ring-2"
      />
      <label
        htmlFor="marketing-cookies-checkbox"
        className="text-md cursor-pointer select-none"
      >
        {label}
      </label>
    </div>
  )
}
