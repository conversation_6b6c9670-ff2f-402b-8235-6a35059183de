'use client'

import React from 'react'

import type { Header as HeaderType } from '@/payload-types'

import { CMSLink } from '@/components/Link'

export const HeaderNav: React.FC<{ data: HeaderType; headerTheme?: string | null }> = ({ data, headerTheme }) => {
  const navItems = data?.navItems || []
  const ctaButton = (data as any)?.ctaButton
  let className = '';

  if(headerTheme === 'light') {
    className = 'hover:border-navy hover:text-navy'
  }

  return (
    <nav className="flex gap-3 items-center">
      {navItems.map(({ link }, i) => {
        return <CMSLink key={i} {...link} appearance="link" />
      })}
      {ctaButton && (
        <CMSLink {...ctaButton} className={className} />
      )}
    </nav>
  )
}
