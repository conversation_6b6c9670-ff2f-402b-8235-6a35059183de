'use client'
import React, { useState, useEffect } from 'react'

export interface AnalyticsCookiesCheckboxProps {
  label?: string
  className?: string
}

export const AnalyticsCookiesCheckbox: React.FC<AnalyticsCookiesCheckboxProps> = ({ 
  label = "I agree to analytics cookies", 
  className 
}) => {
  const [isChecked, setIsChecked] = useState(true)

  // Load saved preference on mount
  useEffect(() => {
    const savedPreference = localStorage.getItem('analyticsCookiesConsent')
    if (savedPreference !== null) {
      setIsChecked(savedPreference === 'true')
    } else {
      // Default to checked and save the preference
      setIsChecked(true)
      localStorage.setItem('analyticsCookiesConsent', 'true')
    }
  }, [])

  // Save preference when changed
  const handleChange = (checked: boolean) => {
    setIsChecked(checked)
    localStorage.setItem('analyticsCookiesConsent', checked.toString())
    
    // Dispatch custom event for other parts of the app to listen to
    window.dispatchEvent(new CustomEvent('analyticsCookiesChanged', { 
      detail: { enabled: checked } 
    }))

    // If you're using Google Analytics, you can integrate here
    if ((window as any).gtag) {
      (window as any).gtag('consent', 'update', { 
        'analytics_storage': checked ? 'granted' : 'denied' 
      })
    }
  }

  return (
    <div className={`inline-flex items-center gap-2 ${className || ''}`}>
      <input
        type="checkbox"
        id="analytics-cookies-checkbox"
        checked={isChecked}
        onChange={(e) => handleChange(e.target.checked)}
        className="w-4 h-4 text-navy bg-white border-2 border-navy rounded focus:ring-navy focus:ring-2"
      />
      <label 
        htmlFor="analytics-cookies-checkbox"
        className="text-md cursor-pointer select-none"
      >
        {label}
      </label>
    </div>
  )
}
