import type { Block } from 'payload'

export const AnalyticsCookiesCheckbox: Block = {
  slug: 'analyticsCookiesCheckbox',
  interfaceName: 'AnalyticsCookiesCheckboxBlock',
  fields: [
    {
      name: 'label',
      type: 'text',
      label: 'Checkbox Label',
      defaultValue: 'I agree to analytics cookies',
      admin: {
        description: 'The text that appears next to the analytics checkbox',
      },
    },
  ],
  labels: {
    plural: 'Analytics Cookies Checkboxes',
    singular: 'Analytics Cookies Checkbox',
  },
}
