import { cn } from '@/utilities/ui'
import * as React from 'react'

const Input: React.FC<
  {
    ref?: React.Ref<HTMLInputElement>
  } & React.InputHTMLAttributes<HTMLInputElement>
> = ({ type, className, ref, ...props }) => {
  return (
    <input
      className={cn(
        'flex w-full file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        className,
      )}
      style={{
        color: 'rgb(24, 38, 88)',
        border: '2px solid rgb(24, 38, 88)',
        backgroundColor: 'rgb(255, 255, 255)',
        fontFamily: '"Ringside Regular A", "Ringside Regular B"',
        fontStyle: 'normal',
        fontWeight: '700',
        borderRadius: '0px',
        appearance: 'none',
        width: '100%',
        padding: '6px',
        fontSize: '16px',
      }}
      ref={ref}
      type={type}
      {...props}
    />
  )
}

export { Input }
