import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

const BASIC_AUTH_USERNAME = 'admintest'
const BASIC_AUTH_PASSWORD = 'secret123'

export function middleware(request: NextRequest) {
  const basicAuth = request.headers.get('authorization')

  if (basicAuth) {
    const [scheme, encoded] = basicAuth.split(' ')

    if (scheme === 'Basic') {
      const decoded = atob(encoded)
      const [user, pass] = decoded.split(':')

      if (user === BASIC_AUTH_USERNAME && pass === BASIC_AUTH_PASSWORD) {
        return NextResponse.next()
      }
    }
  }

  return new Response('Authentication required', {
    status: 401,
    headers: {
      'WWW-Authenticate': 'Basic realm="Protected Area"',
    },
  })
}
