import type { GlobalConfig } from 'payload'

import { link } from '@/fields/link'
import { revalidateHeader } from './hooks/revalidateHeader'

export const Header: GlobalConfig = {
  slug: 'header',
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'gtmId',
      type: 'text',
      label: 'Google Tag Manager ID',
      admin: {
        description: 'Enter your GTM container ID (e.g., GTM-XXXXXXX)',
        placeholder: 'GTM-XXXXXXX',
      },
    },
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false,
        }),
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/Header/RowLabel#RowLabel',
        },
      },
    },
    link({
      appearances: ['default', 'outline'],
      overrides: {
        name: 'cta<PERSON>utton',
        label: 'CTA Button',
      },
    }),
  ],
  hooks: {
    afterChange: [revalidateHeader],
  },
}
