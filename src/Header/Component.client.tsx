'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import type { Header } from '@/payload-types'

import { Logo } from '@/components/Logo/Logo'
import { HeaderNav } from './Nav'

interface HeaderClientProps {
  data: Header
}

export const HeaderClient: React.FC<HeaderClientProps> = ({ data }) => {
  /* Storing the value in a useState to avoid hydration errors */
  const [theme, setTheme] = useState<string | null>('light') // Default to light theme
  const { headerTheme, setHeaderTheme } = useHeaderTheme()
  const pathname = usePathname()

  useEffect(() => {
    // Set default theme on pathname change, heroes can override this
    setHeaderTheme('light') // Default to light theme, heroes will override if needed
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname])

  useEffect(() => {
    if (headerTheme !== undefined && headerTheme !== theme) {
      setTheme(headerTheme)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [headerTheme])

  return (
    <header className="max-w-[1122px] mx-auto relative z-20 w-full min-h-[110px]">
      <div className="p-[26px] flex justify-end md:justify-between relative">
        <Link className="h-[58px] w-[101px] absolute top-[26px] left-[calc(50%-50.5px)] md:relative md:top-auto md:left-auto" href="/">
          <Logo className="h-[58px] w-[101px]" headerTheme={headerTheme} />
        </Link>
        <HeaderNav data={data} headerTheme={headerTheme} />
      </div>
    </header>
  )
}
