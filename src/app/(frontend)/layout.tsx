import type { <PERSON>ada<PERSON> } from 'next'

import React from 'react'

import { AdminBar } from '@/components/AdminBar'
import { BodyBackgroundSetter } from '@/components/BackgroundWrapper'
import { Footer } from '@/Footer/Component'
import { Header } from '@/Header/Component'
import { GTM } from '@/components/GTM'
import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'
import { mergeOpenGraph } from '@/utilities/mergeOpenGraph'
import { draftMode } from 'next/headers'
import { getCachedGlobal } from '@/utilities/getGlobals'
import type { Header as HeaderType } from '@/payload-types'

import './globals.css'
import { getServerSideURL } from '@/utilities/getURL'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const { isEnabled } = await draftMode()
  const headerData = await getCachedGlobal('header', 1)() as HeaderType

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <InitTheme />
        <link rel="stylesheet" href="https://use.typekit.net/ani8hja.css" />
        <link href="/favicon.ico" rel="icon" sizes="32x32" />
        <link href="/favicon.svg" rel="icon" type="image/svg+xml" />
      </head>
      <body>
        <Providers>
          <BodyBackgroundSetter />
          <GTM gtmId={headerData?.gtmId} />
          <AdminBar
            adminBarProps={{
              preview: isEnabled,
            }}
          />

          <Header />
          {children}
          <Footer />
        </Providers>
      </body>
    </html>
  )
}

export const metadata: Metadata = {
  metadataBase: new URL(getServerSideURL()),
  openGraph: mergeOpenGraph(),
  twitter: {
    card: 'summary_large_image',
    creator: '',
  },
}
