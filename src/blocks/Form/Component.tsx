'use client'
import type { FormFieldBlock, Form as FormType } from '@payloadcms/plugin-form-builder/types'

import { useRouter } from 'next/navigation'
import React, { useCallback, useState, useEffect } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import RichText from '@/components/RichText'
import { Button } from '@/components/ui/button'
import type { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'

import { fields } from './fields'
import { getClientSideURL } from '@/utilities/getURL'

export type FormBlockType = {
  blockName?: string
  blockType?: 'formBlock'
  enableIntro: boolean
  form: FormType
  introContent?: SerializedEditorState
}

export const FormBlock: React.FC<
  {
    id?: string
  } & FormBlockType
> = (props) => {
  const {
    enableIntro,
    form: formFromProps,
    form: { id: formID, confirmationMessage, confirmationType, redirect, submitButtonLabel, title } = {},
    introContent
  } = props

  // Safely extract intro, submitButtonWidth, disclaimer, and actionKitPage from form with type assertion
  const intro = (formFromProps as any)?.intro
  const submitButtonWidth = (formFromProps as any)?.submitButtonWidth || 100
  const disclaimer = (formFromProps as any)?.disclaimer
  const actionKitPage = (formFromProps as any)?.actionKitPage

  const formMethods = useForm({
    defaultValues: formFromProps.fields,
  })
  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
  } = formMethods

  const [isLoading, setIsLoading] = useState(false)
  const [hasSubmitted, setHasSubmitted] = useState<boolean>()
  const [error, setError] = useState<{ message: string; status?: string } | undefined>()
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null)
  const router = useRouter()

  // Use client-side only search params to avoid SSR issues
  useEffect(() => {
    // Only access search params on the client side
    if (typeof window !== 'undefined') {
      setSearchParams(new URLSearchParams(window.location.search))
    }
  }, [])

  // Function to get current source parameter
  const getCurrentSource = () => {
    if (typeof window !== 'undefined') {
      const currentParams = new URLSearchParams(window.location.search)
      const sourceParam = currentParams.get('source')
      const refcodeParam = currentParams.get('refcode')
      return sourceParam || refcodeParam
    }
    return null
  }

  // Get source parameter from URL, fallback to refcode if source is empty
  const sourceParam = searchParams?.get('source')
  const refcodeParam = searchParams?.get('refcode')
  const source = sourceParam || refcodeParam

  const onSubmit = useCallback(
    (data: FormFieldBlock[]) => {
      let loadingTimerID: ReturnType<typeof setTimeout>
      const submitForm = async () => {
        setError(undefined)

        const dataToSend = Object.entries(data).map(([name, value]) => ({
          field: name,
          value,
        }))

        // Add source parameter if it exists in URL (get fresh value)
        const currentSource = getCurrentSource()
        if (currentSource) {
          dataToSend.push({
            field: 'source',
            value: currentSource as any,
          })
        }

        // delay loading indicator by 1s
        loadingTimerID = setTimeout(() => {
          setIsLoading(true)
        }, 1000)

        try {
          // Submit to local API endpoint (which will handle Action Kit if needed)
          const req = await fetch(`${getClientSideURL()}/api/form-submissions`, {
            body: JSON.stringify({
              form: formID,
              submissionData: dataToSend,
              actionKitPage: actionKitPage,
            }),
            headers: {
              'Content-Type': 'application/json',
            },
            method: 'POST',
          })

          const res = await req.json()

          clearTimeout(loadingTimerID)

          if (req.status >= 400) {
            setIsLoading(false)

            setError({
              message: res.errors?.[0]?.message || 'Internal Server Error',
              status: res.status,
            })

            return
          }

          setIsLoading(false)
          setHasSubmitted(true)

          if (confirmationType === 'redirect' && redirect) {
            const { url } = redirect
            let redirectUrl = url

            // Add refcode parameter to ActBlue URLs if source exists
            if (redirectUrl && currentSource && redirectUrl.toLowerCase().includes('actblue')) {
              try {
                const urlObj = new URL(redirectUrl)
                urlObj.searchParams.set('refcode', currentSource)
                redirectUrl = urlObj.toString()
              } catch (error) {
                console.warn('Failed to parse redirect URL for refcode:', error)
              }
            }

            if (redirectUrl) router.push(redirectUrl)
          }
        } catch (err) {
          console.warn(err)
          setIsLoading(false)
          setError({
            message: 'Something went wrong.',
          })
        }
      }

      void submitForm()
    },
    [router, formID, redirect, confirmationType],
  )

  return (
    <div>
      {/* Show form title */}
      {title && !hasSubmitted && (
        <h2 className="mb-[13px] font-extra-wide text-navy uppercase">{title}</h2>
      )}
      {/* Show form's built-in intro if it exists */}
      {intro && !hasSubmitted && (
        <RichText className="mb-[15px] font-regular-book leading-[-0.2px] text-[16px] line-height-[1.6] text-black" data={intro} enableGutter={false} enableProse={false} />
      )}
      {/* Show block-level intro if enabled and no form intro */}
      {!intro && enableIntro && introContent && !hasSubmitted && (
        <RichText className="mb-[15px]" data={introContent} enableGutter={false} />
      )}
      <div>
        <FormProvider {...formMethods}>
          {!isLoading && hasSubmitted && confirmationType === 'message' && (
            <RichText data={confirmationMessage} />
          )}
          {isLoading && !hasSubmitted && <p>Loading, please wait...</p>}
          {error && <div>{`${error.status || '500'}: ${error.message || ''}`}</div>}
          {!hasSubmitted && (
            <form id={formID} onSubmit={handleSubmit(onSubmit)} className="flex flex-row flex-wrap items-start gap-[10px]">
              {formFromProps &&
                formFromProps.fields &&
                formFromProps.fields?.map((field, index) => {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const Field: React.FC<any> = fields?.[field.blockType as keyof typeof fields]
                  if (Field) {
                    return (
                        <Field
                          form={formFromProps}
                          {...field}
                          {...formMethods}
                          control={control}
                          errors={errors}
                          register={register}
                          key={index}
                        />
                    )
                  }
                  return null
                })}
              <Button
                form={formID}
                type="submit"
                variant="secondary"
                style={{ width: `calc(${submitButtonWidth}% - 10px)`, marginTop: '20px' }}
              >
                {submitButtonLabel}
              </Button>

              {/* Show disclaimer if it exists */}
              {disclaimer && (
                <div className="text-sm text-navy font-regular-book mt-[5px]">
                  <RichText data={disclaimer} enableGutter={false} enableProse={false} className='text-sm font-regular-book text-black-60' />
                </div>
              )}
            </form>
          )}
        </FormProvider>
      </div>
    </div>
  )
}
