@tailwind base;
@tailwind components;
@tailwind utilities;

/*
  Copyright (C) Hoefler & Co.
  This software is the property of Hoefler & Co. (H&Co).
  Your right to access and use this software is subject to the
  applicable License Agreement, or Terms of Service, that exists
  between you and H&Co. If no such agreement exists, you may not
  access or use this software for any purpose.
  This software may only be hosted at the locations specified in
  the applicable License Agreement or Terms of Service, and only
  for the purposes expressly set forth therein. You may not copy,
  modify, convert, create derivative works from or distribute this
  software in any way, or make it accessible to any third party,
  without first obtaining the written permission of H&Co.
  For more information, please visit us at http://typography.com.
*/

@font-face {
  font-family: 'Ringside Regular A';
  src: url('/fonts/RingsideRegular-Book_Web.woff2') format('woff2'), url('/fonts/RingsideRegular-Book_Web.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Ringside Regular A';
  src: url('/fonts/RingsideRegular-BookItalic_Web.woff2') format('woff2'), url('/fonts/RingsideRegular-BookItalic_Web.woff') format('woff');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Ringside Regular A';
  src: url('/fonts/RingsideRegular-Bold_Web.woff2') format('woff2'), url('/fonts/RingsideRegular-Bold_Web.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Ringside Regular A';
  src: url('/fonts/RingsideRegular-BoldItalic_Web.woff2') format('woff2'), url('/fonts/RingsideRegular-BoldItalic_Web.woff') format('woff');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Ringside Extra Wide SSm A';
  src: url('/fonts/RingsideExtraWideSSm-Black_Web.woff2') format('woff2'), url('/fonts/RingsideExtraWideSSm-Black_Web.woff') format('woff');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Ringside Compressed A';
  src: url('/fonts/RingsideCompressed-Bold_Web.woff2') format('woff2'), url('/fonts/RingsideCompressed-Bold_Web.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}



@layer utilities {
  .font-regular-book {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-feature-settings: "liga" 0, "clig" 0;
    font-style: normal;
    font-weight: 400;
  }

  .font-regular-book-italic {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-feature-settings: "liga" 0, "clig" 0;
    font-style: italic;
    font-weight: 400;
  }

  .font-regular-book-bold {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-feature-settings: "liga" 0, "clig" 0;
    font-style: normal;
    font-weight: 700;
  }

  .font-regular-book-bold-italic {
    font-family: "Ringside Regular A", "Ringside Regular B";
    font-feature-settings: "liga" 0, "clig" 0;
    font-style: italic;
    font-weight: 700;
  }

  .font-compressed-bold {
    font-family: "Ringside Compressed A", "Ringside Compressed B";
    font-style: normal;
    font-weight: 700;
  }

  .font-extra-wide {
    font-family: "Ringside Extra Wide SSm A", "Ringside Extra Wide SSm B";
    font-style: normal;
    font-weight: 800;
  }

  .font-serif-book {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 400;
    font-style: normal;
  }

  .font-serif-book-italic {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 400;
    font-style: italic;
  }

  .font-serif-bold {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 700;
    font-style: normal;
  }

  .font-serif-bold-italic {
    font-family: freight-text-pro, Georgia, serif;
    font-weight: 700;
    font-style: italic;
  }

  /* Rich text inline links styling */
  .payload-richtext a {
    font-weight: bold;
    word-break: break-word;
  }

  /* Rich text inline links styling */
  .payload-richtext a:hover {
    text-decoration: underline;
  }
}

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: unset;
    font-weight: unset;
  }

  :root {
    --navy: #182658;
    --blue: #2A79AD;
    --black: #000;
    --background: #fff;
    --foreground: 222.2 84% 4.9%;

    --card: 240 5% 96%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: #182658;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: #B7E4CF;
    --input: rgb(228, 244, 238);
    --ring: #B7E4CF;

    --radius: 0.2rem;

    --success: 196 52% 74%;
    --warning: 34 89% 85%;
    --error: rgb(188, 17, 38);
  }


}

@layer base {
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
  }
}

html {
  opacity: 0;
}

html {
  opacity: initial;
}
