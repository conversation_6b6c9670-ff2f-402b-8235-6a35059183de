import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { actionKitData } = body

    if (!actionKitData || !actionKitData.page) {
      return NextResponse.json(
        { error: 'Action Kit page is required' },
        { status: 400 }
      )
    }

    // Submit to Action Kit API
    const actionKitResponse = await fetch('https://act.elizabethwarren.com/rest/v1/action', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
      },
      body: JSON.stringify(actionKitData),
    })

    if (!actionKitResponse.ok) {
      const errorText = await actionKitResponse.text()
      return NextResponse.json(
        { 
          error: 'Action Kit submission failed',
          details: errorText,
          status: actionKitResponse.status 
        },
        { status: actionKitResponse.status }
      )
    }

    const actionKitResult = await actionKitResponse.json()
    
    return NextResponse.json({
      success: true,
      actionKitResult,
    })

  } catch (error) {
    console.error('Action Kit API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
