import tailwindcssAnimate from 'tailwindcss-animate'
import typography from '@tailwindcss/typography'

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],

  prefix: '',
  safelist: [
    'lg:col-span-4',
    'lg:col-span-6',
    'lg:col-span-8',
    'lg:col-span-12',
    'border-red',
    'bg-card',
    'border-error',
    'bg-error/30',
    'border-success',
    'bg-success/30',
    'border-warning',
    'bg-warning/30',
  ],
  theme: {
    container: {
      center: true,
      padding: {
        '2xl': '2rem',
        DEFAULT: '1rem',
        lg: '2rem',
        md: '2rem',
        sm: '1rem',
        xl: '2rem',
      },
      screens: {
        '2xl': '1440px',
        lg: '1024px',
        md: '768px',
        sm: '480px',
        xl: '1220px',
      },
    },
    extend: {
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      backgroundImage: {
        'blue-gradient': 'linear-gradient(180deg, rgba(24, 38, 88, 0.50) 0%, rgba(24, 38, 88, 0.00) 44.42%)',
      },
      backgroundBlendMode: {
        'multiply': 'multiply',
      },
      colors: {
        navy: '#182658',
        purple: '#575893',
        black: '#000',
        blue: '#2A79AD',
        red: '#B61B28',
        'black-60': 'rgba(0, 0, 0, 0.60)',
        white: '#fff',
        'light-liberty': '#E3F4EC',
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        background: 'var(--background)',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        foreground: 'hsl(var(--foreground))',
        input: 'var(--input)',
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'var(--primary)',
          foreground: 'hsl(var(--primary-foreground))',
        },
        ring: 'var(--ring)',
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        success: 'hsl(var(--success))',
        error: 'var(--error)',
        warning: 'hsl(var(--warning))',
      },
      fontFamily: {
        mono: ['mono'],
        sans: ['Ringside Compressed A', 'sans-serif'],
        'ringside': ['Ringside Regular A', 'sans-serif'],
        'ringside-wide': ['Ringside Extra Wide SSm A', 'sans-serif'],
        'ringside-compressed': ['Ringside Compressed A', 'sans-serif'],
      },
      fontWeight: {
        325: '325',
      },
      fontSize: {
        'xs-label': ['12px', '15px'],
        'sm': ['11px', '15px'],
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      typography: () => ({
        DEFAULT: {
          css: [
            {
              maxWidth: '900px',
              '--tw-prose-body': 'var(--black)',
              '--tw-prose-headings': 'var(--navy)',
              '--tw-prose-lead': 'var(--black)',
              '--tw-prose-links': 'var(--navy)',
              '--tw-prose-bold': 'var(--black)',
              '--tw-prose-counters': 'var(--navy)',
              '--tw-prose-bullets': 'var(--navy)',
              '--tw-prose-hr': 'var(--navy)',
              '--tw-prose-quotes': 'var(--black)',
              '--tw-prose-quote-borders': 'var(--navy)',
              '--tw-prose-captions': 'var(--black)',
              '--tw-prose-code': 'var(--navy)',
              '--tw-prose-pre-code': 'var(--black)',
              '--tw-prose-pre-bg': 'var(--light-liberty)',
              '--tw-prose-th-borders': 'var(--navy)',
              '--tw-prose-td-borders': 'var(--navy)',
              color: 'var(--black)',
              fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
              fontSize: '18px',
              fontStyle: 'normal',
              fontWeight: '500',
              lineHeight: '24px',
              letterSpacing: '-0.079px',
              h1: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Compressed A", "Ringside Compressed B"',
                fontSize: '32px',
                fontStyle: 'normal',
                fontWeight: '700',
                lineHeight: '30px',
                letterSpacing: '-0.222px',
                textTransform: 'uppercase',
                marginBottom: '20px',
              },
              h2: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Compressed A", "Ringside Compressed B"',
                fontSize: '26px',
                fontStyle: 'normal',
                fontWeight: '700',
                lineHeight: '24.44px',
                letterSpacing: '-0.222px',
                textTransform: 'uppercase',
                marginBottom: '20px',
              },
              h3: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Extra Wide SSm A",sans-serif',
                fontSize: '16px',
                fontStyle: 'normal',
                fontWeight: '800',
                lineHeight: '25.44px',
                textTransform: 'uppercase',
                marginBottom: '18px',
              },
              h4: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Regular A", "Ringside Regular B",sans-serif',
                fontSize: '24px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: '1.34',
                marginBottom: '18px',
              },
              h5: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Extra Wide SSm A",sans-serif',
                fontSize: '12px',
                fontStyle: 'normal',
                fontWeight: '700',
                lineHeight: '1.17',
                marginBottom: '18px',
                textTransform: 'uppercase',
              },
              p: {
                color: 'var(--black)',
                fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
                marginBottom: '20px',
                marginTop: '20px',
              },
            },
          ],
        },
        base: {
          css: [
            {
              color: 'var(--black)',
              fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
              fontSize: '18px',
              fontWeight: '500',
              lineHeight: '24px',
              letterSpacing: '-0.079px',
              h1: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Compressed A", "Ringside Compressed B"',
                fontSize: '32px',
                fontStyle: 'normal',
                fontWeight: '700',
                lineHeight: '30px',
                letterSpacing: '-0.222px',
                textTransform: 'uppercase',
                marginBottom: '20px',
              },
              h2: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Compressed A", "Ringside Compressed B"',
                fontSize: '26px',
                fontStyle: 'normal',
                fontWeight: '700',
                lineHeight: '24.44px',
                letterSpacing: '-0.222px',
                textTransform: 'uppercase',
                marginBottom: '20px',
              },
              h3: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Extra Wide SSm A",sans-serif',
                fontSize: '16px',
                fontStyle: 'normal',
                fontWeight: '800',
                lineHeight: '25.44px',
                textTransform: 'uppercase',
                marginBottom: '18px',
              },
              h4: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Regular A", "Ringside Regular B",sans-serif',
                fontSize: '24px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: '1.34',
                marginBottom: '18px',
              },
              h5: {
                color: 'var(--primary)',
                fontFamily: '"Ringside Extra Wide SSm A",sans-serif',
                fontSize: '12px',
                fontStyle: 'normal',
                fontWeight: '700',
                lineHeight: '1.17',
                marginBottom: '18px',
                textTransform: 'uppercase',
              },
              p: {
                color: 'var(--black)',
                fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
                marginBottom: '20px',
                marginTop: '20px',
              },
              ul: {
                color: 'var(--black)',
                fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
                marginBottom: '20px',
                marginTop: '20px',
              },
              ol: {
                color: 'var(--black)',
                fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
                marginBottom: '20px',
                marginTop: '20px',
              },
              li: {
                color: 'var(--black)',
                fontFamily: '"FreightText Pro", freight-text-pro, Georgia, serif',
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
              },
            },
          ],
        },
        md: {
          css: [
            {
              fontSize: '18px',
              lineHeight: '24px',
              h1: {
                fontSize: '50px',
                lineHeight: '47px',
              },
              p: {
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
              },
              ul: {
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
              },
              ol: {
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
              },
              li: {
                fontSize: '18px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                letterSpacing: '-0.079px',
              },
            },
          ],
        },
        lg: {
          css: [
            {
              fontSize: '18px',
              lineHeight: '24px',
              h1: {
                fontSize: '60px',
                lineHeight: '56px',
                marginBottom: '32px',
              },
              p: {
                fontSize: '22px',
                lineHeight: '28px',
                letterSpacing: '-0.079px',
              },
              ul: {
                fontSize: '22px',
                lineHeight: '28px',
                letterSpacing: '-0.079px',
              },
              ol: {
                fontSize: '22px',
                lineHeight: '28px',
                letterSpacing: '-0.079px',
              },
              li: {
                fontSize: '22px',
                lineHeight: '28px',
                letterSpacing: '-0.079px',
              },
            },
          ],
        },
      }),
    },
  },
  plugins: [
    tailwindcssAnimate,
    typography,
    function({ addUtilities }) {
      addUtilities({
        '.gradient-overlay': {
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            inset: '0',
            background: 'linear-gradient(180deg, rgba(24, 38, 88, 0.50) 0%, rgba(24, 38, 88, 0.00) 44.42%)',
            'background-blend-mode': 'multiply',
            'z-index': '10',
          },
        },
        '.form-label': {
          color: '#000',
          fontFamily: '"Ringside Extra Wide SSm A"',
          fontSize: '12px',
          fontWeight: '300',
          lineHeight: '15px',
          fontFeatureSettings: "'liga' off, 'clig' off",
        },
      })
    },
  ],
}

export default config
