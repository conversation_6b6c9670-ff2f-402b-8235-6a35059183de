import { getCachedGlobal } from '@/utilities/getGlobals'
import Link from 'next/link'
import React from 'react'

import type { Footer } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'

export async function Footer() {
  const footerData: Footer = await getCachedGlobal('footer', 1)()

  const navItems = footerData?.navItems || []
  const { disclaimer, copyright } = footerData || {}

  return (
    <footer className="mt-auto border-t-[6px] bg-white w-full" style={{ borderTopColor: 'var(--border)', color: '#182658' }}>
      <div className="py-[45px] px-[26px] flex flex-col gap-[30px] max-w-[1242px] mx-auto w-full md:flex-row md:justify-between md:py-[65px]">
        {/* Main footer content */}
          <Link className="flex items-center justify-center md:order-2 md:justify-center" href="/">
            <Logo fightColor='#182658' pacColor='#182658' className='h-[73px] w-[127px] md:h-[93px] md:w-[161px]'/>
          </Link>
          <div className="flex flex-col items-center md:flex-row md:items-start md:justify-start md:w-[35%]">
            <nav className="flex flex-col gap-[15px]">
              {navItems.map(({ link }, i) => {
                return (
                  <div key={i} className='text-center md:text-left'>
                    <CMSLink
                      className="text-navy hover:text-purple text-center md:text-left text-base font-bold leading-1 font-regular-book-bold"
                      {...link}
                    />
                  </div>
                )
              })}
            </nav>
          </div>

        {/* Disclaimer and Copyright section */}
        <div className="flex flex-col gap-[15px] items-center text-sm text-gray-300 md:order-3 md:w-[35%] md:items-end md:justify-start">
          {disclaimer && (
            <div className="disclaimer">
              <p
                className="text-navy text-center font-regular-book text-[10px] font-light tracking-[1.6px] leading-[1.2] p-[10px] uppercase w-full border border-navy/30"
                style={{ fontWeight: 300 }}
              >
                {disclaimer}
              </p>
            </div>
          )}

          {copyright && (
            <div className="font-regular-book text-navy text-center md:text-right w-fit">
              <p>{copyright}</p>
            </div>
          )}
        </div>
      </div>
    </footer>
  )
}
