import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'

import { redirectsPlugin } from '@payloadcms/plugin-redirects'
import { seoPlugin } from '@payloadcms/plugin-seo'

import { Plugin } from 'payload'
import { revalidateRedirects } from '@/hooks/revalidateRedirects'
import { GenerateTitle, GenerateURL } from '@payloadcms/plugin-seo/types'
import { FixedToolbarFeature, HeadingFeature, lexicalEditor, OrderedListFeature, UnorderedListFeature, BlocksFeature } from '@payloadcms/richtext-lexical'
import { MarketingCookiesCheckbox } from '@/blocks/MarketingCookiesCheckbox/config'
import { AnalyticsCookiesCheckbox } from '@/blocks/AnalyticsCookiesCheckbox/config'


import { Page } from '@/payload-types'
import { getServerSideURL } from '@/utilities/getURL'

const generateTitle: GenerateTitle<Page> = ({ doc }) => {
  return doc?.title ? `${doc.title} | Fight PAC` : 'Fight PAC'
}

const generateURL: GenerateURL<Page> = ({ doc }) => {
  const url = getServerSideURL()

  return doc?.slug ? `${url}/${doc.slug}` : url
}

export const plugins: Plugin[] = [
  redirectsPlugin({
    collections: ['pages'],
    overrides: {
      // @ts-expect-error - This is a valid override, mapped fields don't resolve to the same type
      fields: ({ defaultFields }) => {
        return defaultFields.map((field) => {
          if ('name' in field && field.name === 'from') {
            return {
              ...field,
              admin: {
                description: 'You will need to rebuild the website when changing this field.',
              },
            }
          }
          return field
        })
      },
      hooks: {
        afterChange: [revalidateRedirects],
      },
    },
  }),

  seoPlugin({
    generateTitle,
    generateURL,
  }),
  formBuilderPlugin({
    fields: {
      payment: false,
    },
    formOverrides: {
      fields: ({ defaultFields }) => {
        const actionKitPageField = {
          name: 'actionKitPage',
          type: 'text',
          label: 'Action Kit Page',
          admin: {
            description: 'Action Kit page identifier for form submission',
          },
        }
        const introField = {
          name: 'intro',
          type: 'richText',
          label: 'Form Intro',
          admin: {
            description: 'Optional intro content that appears above the form fields',
          },
          editor: lexicalEditor({
            features: ({ rootFeatures }) => {
              return [
                ...rootFeatures,
                OrderedListFeature(),
                UnorderedListFeature(),
                BlocksFeature({
                  blocks: [MarketingCookiesCheckbox, AnalyticsCookiesCheckbox],
                }),
                FixedToolbarFeature(),
                HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
              ]
            },
          }),
        }

        const modifiedFields: any[] = []

        defaultFields.forEach((field, index) => {
          // Add the current field first
          if ('name' in field && field.name === 'submitButtonLabel') {
            modifiedFields.push(field)
            // Add submit button width right after submit button label
            modifiedFields.push({
              name: 'submitButtonWidth',
              type: 'number',
              label: 'Submit Button Width (%)',
              defaultValue: 100,
              min: 0,
              max: 100,
              hasMany: false,
              admin: {
                description: 'Submit button width as a percentage (0-100%). 100% = full width, 50% = half width, etc.',
              },
            })
          } else if ('name' in field && field.name === 'confirmationMessage') {
            // Add disclaimer before confirmation message
            modifiedFields.push({
              name: 'disclaimer',
              type: 'richText',
              label: 'Form Disclaimer',
              admin: {
                description: 'Optional disclaimer text that appears below the form fields',
              },
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    OrderedListFeature(),
                    UnorderedListFeature(),
                    BlocksFeature({
                      blocks: [MarketingCookiesCheckbox, AnalyticsCookiesCheckbox],
                    }),
                    FixedToolbarFeature(),
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                  ]
                },
              }),
            })
            // Then add the confirmation message field
            modifiedFields.push({
              ...field,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    OrderedListFeature(),
                    UnorderedListFeature(),
                    BlocksFeature({
                      blocks: [MarketingCookiesCheckbox, AnalyticsCookiesCheckbox],
                    }),
                    FixedToolbarFeature(),
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                  ]
                },
              }),
            })
          } else if ('name' in field && field.name === 'fields' && field.type === 'blocks') {
            modifiedFields.push({
              ...field,
              blocks: [
                ...field.blocks,
                {
                  slug: 'phone',
                  labels: {
                    singular: 'Phone',
                    plural: 'Phone Fields',
                  },
                  fields: [
                    {
                      name: 'name',
                      type: 'text',
                      label: 'Name (lowercase, no spaces)',
                      required: true,
                    },
                    {
                      name: 'label',
                      type: 'text',
                      label: 'Label',
                    },
                    {
                      name: 'width',
                      type: 'number',
                      label: 'Field Width (%)',
                      defaultValue: 100,
                      max: 100,
                      min: 25,
                    },
                    {
                      name: 'required',
                      type: 'checkbox',
                      label: 'Required',
                    },
                  ],
                },
                {
                  slug: 'zipCode',
                  labels: {
                    singular: 'Zip Code',
                    plural: 'Zip Code Fields',
                  },
                  fields: [
                    {
                      name: 'name',
                      type: 'text',
                      label: 'Name (lowercase, no spaces)',
                      required: true,
                    },
                    {
                      name: 'label',
                      type: 'text',
                      label: 'Label',
                    },
                    {
                      name: 'width',
                      type: 'number',
                      label: 'Field Width (%)',
                      defaultValue: 100,
                      max: 100,
                      min: 25,
                    },
                    {
                      name: 'required',
                      type: 'checkbox',
                      label: 'Required',
                    },
                  ],
                },
              ],
            })
          } else {
            modifiedFields.push(field)
          }

          // Insert intro field and actionKitPage field after title field (first field)
          if (index === 0) {
            modifiedFields.push(introField)
            modifiedFields.push(actionKitPageField)
          }
        })



        return modifiedFields
      },
    },
  }),

  payloadCloudPlugin(),
]
