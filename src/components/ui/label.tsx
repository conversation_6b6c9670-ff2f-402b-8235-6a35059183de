'use client'

import { cn } from '@/utilities/ui'
import * as LabelPrimitive from '@radix-ui/react-label'
import { type VariantProps, cva } from 'class-variance-authority'
import * as React from 'react'

const labelVariants = cva(
  'block text-xs-label font-regular-book text-black mb-[5px] leading-[15px] peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
)

const Label: React.FC<
  { ref?: React.Ref<HTMLLabelElement> } & React.ComponentProps<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
> = ({ className, ref, style, ...props }) => (
  <LabelPrimitive.Root
    className={cn(labelVariants(), className)}
    ref={ref}
    style={{
      fontFeatureSettings: "'liga' off, 'clig' off",
      ...style
    }}
    {...props}
  />
)

export { Label }
