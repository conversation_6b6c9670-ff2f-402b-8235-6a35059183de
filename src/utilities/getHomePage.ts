import type { Page } from '@/payload-types'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { unstable_cache } from 'next/cache'

/**
 * Fetches the home page data from the CMS
 */
async function getHomePage(depth = 2): Promise<Page | null> {
  const payload = await getPayload({ config: configPromise })

  const result = await payload.find({
    collection: 'pages',
    where: {
      slug: {
        equals: 'home',
      },
    },
    depth, // Include related media and other references
    limit: 1,
  })

  return result.docs[0] || null
}

/**
 * Returns a cached version of the home page data
 * This is useful for performance optimization
 */
export const getCachedHomePage = (depth = 2) =>
  unstable_cache(async () => getHomePage(depth), ['home-page'], {
    tags: ['pages_home'],
  })

/**
 * Direct function to get home page (non-cached)
 * Use this when you need fresh data or in server actions
 */
export { getHomePage }
