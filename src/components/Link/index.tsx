'use client'
import { Button, type ButtonProps } from '@/components/ui/button'
import { cn } from '@/utilities/ui'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'

import type { Page } from '@/payload-types'

type CMSLinkType = {
  appearance?: 'inline' | ButtonProps['variant']
  children?: React.ReactNode
  className?: string
  label?: string | null
  newTab?: boolean | null
  reference?: {
    relationTo: 'pages'
    value: Page | string | number
  } | null
  size?: ButtonProps['size'] | null
  type?: 'custom' | 'reference' | null
  url?: string | null
}

export const CMSLink: React.FC<CMSLinkType> = (props) => {
  const {
    type,
    appearance = 'inline',
    children,
    className,
    label,
    newTab,
    reference,
    size: sizeFromProps,
    url,
  } = props

  // Use client-side only search params to avoid SSR issues
  const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null)

  useEffect(() => {
    // Only access search params on the client side
    if (typeof window !== 'undefined') {
      setSearchParams(new URLSearchParams(window.location.search))
    }
  }, [])

  const href =
    type === 'reference' && typeof reference?.value === 'object' && reference.value.slug
      ? `${reference?.relationTo !== 'pages' ? `/${reference?.relationTo}` : ''}/${
          reference.value.slug
        }`
      : url

  if (!href) return null

  const size = appearance === 'link' ? 'clear' : sizeFromProps
  const newTabProps = newTab ? { rel: 'noopener noreferrer', target: '_blank' } : {}

  // Function to add refcode to ActBlue URLs
  const getUrlWithRefcode = (originalUrl: string): string => {
    // Only process refcode on client side when search params are available
    if (!searchParams) {
      return originalUrl
    }

    const refcodeParam = searchParams.get('refcode')
    const sourceParam = searchParams.get('source')
    const refcode = refcodeParam || sourceParam

    // Only add refcode if it exists and URL contains "actblue"
    if (!refcode || !originalUrl.toLowerCase().includes('actblue')) {
      return originalUrl
    }

    try {
      const urlObj = new URL(originalUrl)
      urlObj.searchParams.set('refcode', refcode)
      return urlObj.toString()
    } catch {
      // If URL parsing fails, return original URL
      return originalUrl
    }
  }

  /* Ensure we don't break any styles set by richText */
  if (appearance === 'inline') {
    return (
      <Link className={cn(className)} href={href || url || ''} {...newTabProps}>
        {label && label}
        {children && children}
      </Link>
    )
  }

  return (
    <Button asChild className={className} size={size} variant={appearance}>
      <Link className={cn(className)} href={getUrlWithRefcode(href || url || '')} {...newTabProps}>
        {label && label}
        {children && children}
      </Link>
    </Button>
  )
}
