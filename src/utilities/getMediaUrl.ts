import { getClientSideURL } from '@/utilities/getURL'

/**
 * Processes media resource URL to ensure proper formatting
 * @param url The original URL from the resource
 * @param cacheTag Optional cache tag to append to the URL
 * @returns Properly formatted URL with cache tag if provided
 */
export const getMediaUrl = (url: string | null | undefined, cacheTag?: string | null): string => {
  if (!url) return ''

  let finalUrl = url

  // Check if URL already has http/https protocol
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    // Otherwise prepend client-side URL
    const baseUrl = getClientSideURL()
    finalUrl = `${baseUrl}${url}`
  }

  // Add cache tag as a proper query parameter if provided
  if (cacheTag) {
    try {
      const urlObj = new URL(finalUrl)
      // Use 'v' as the cache parameter name and create a simple hash from the cache tag
      const cacheValue = typeof cacheTag === 'string' ? cacheTag : String(cacheTag)
      // Create a simple hash to avoid issues with special characters
      const hashValue = btoa(cacheValue).replace(/[^a-zA-Z0-9]/g, '').substring(0, 10)
      urlObj.searchParams.set('v', hashValue)
      return urlObj.toString()
    } catch (error) {
      // Fallback to simple concatenation if URL parsing fails
      console.warn('Failed to parse URL for cache tag:', error)
      return finalUrl
    }
  }

  return finalUrl
}
