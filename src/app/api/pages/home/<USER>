import { NextResponse } from 'next/server'
import { getHomePage } from '@/utilities/getHomePage'

/**
 * API route to fetch home page data
 * GET /api/pages/home
 */
export async function GET() {
  try {
    const homePage = await getHomePage(2) // depth 2 to include media relations
    
    if (!homePage) {
      return NextResponse.json(
        { error: 'Home page not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(homePage)
  } catch (error) {
    console.error('Error fetching home page:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
