import type { Block } from 'payload'

export const MarketingCookiesCheckbox: Block = {
  slug: 'marketingCookiesCheckbox',
  interfaceName: 'MarketingCookiesCheckboxBlock',
  fields: [
    {
      name: 'label',
      type: 'text',
      label: 'Checkbox Label',
      defaultValue: 'I agree to receive marketing communications',
      admin: {
        description: 'The text that appears next to the marketing checkbox',
      },
    },
  ],
  labels: {
    plural: 'Marketing Cookies Checkboxes',
    singular: 'Marketing Cookies Checkbox',
  },
}
