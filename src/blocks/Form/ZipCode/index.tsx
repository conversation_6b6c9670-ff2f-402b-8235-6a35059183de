import type { TextField } from '@payloadcms/plugin-form-builder/types'
import type { FieldErrorsImpl, FieldValues, UseFormRegister } from 'react-hook-form'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React from 'react'

import { Error } from '../Error'
import { Width } from '../Width'

export const ZipCode: React.FC<
  TextField & {
    errors: Partial<FieldErrorsImpl>
    register: UseFormRegister<FieldValues>
  }
> = ({ name, defaultValue, errors, label, register, required, width }) => {
  return (
    <Width width={width}>
      <Label htmlFor={name}>
        {label}
        {required ? (
          <span className="sr-only">(required)</span>
        ) : (
          <span className="text-muted-foreground"> (optional)</span>
        )}
      </Label>
      <Input
        defaultValue={defaultValue}
        id={name}
        type="text"
        placeholder=""
        maxLength={10}
        {...register(name, { 
          required,
          pattern: {
            value: /^\d{5}(-\d{4})?$/,
            message: 'Please enter a valid ZIP code (12345 or 12345-6789)'
          }
        })}
      />
      {errors[name] && <Error name={name} />}
    </Width>
  )
}
