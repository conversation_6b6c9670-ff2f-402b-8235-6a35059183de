'use client'
import { useEffect } from 'react'

interface GTMProps {
  gtmId?: string | null
}

export const GTM: React.FC<GTMProps> = ({ gtmId }) => {
  useEffect(() => {
    if (!gtmId || typeof gtmId !== 'string') return

    // Initialize GTM
    const script = document.createElement('script')
    script.innerHTML = `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','${gtmId}');
    `
    document.head.appendChild(script)

    // Add noscript fallback
    const noscript = document.createElement('noscript')
    noscript.innerHTML = `
      <iframe src="https://www.googletagmanager.com/ns.html?id=${gtmId}"
      height="0" width="0" style="display:none;visibility:hidden"></iframe>
    `
    document.body.insertBefore(noscript, document.body.firstChild)

    // Cleanup function
    return () => {
      // Remove GTM script if component unmounts
      const scripts = document.querySelectorAll(`script[src*="googletagmanager.com/gtm.js?id=${gtmId}"]`)
      scripts.forEach(script => script.remove())
      
      const noscripts = document.querySelectorAll(`noscript iframe[src*="googletagmanager.com/ns.html?id=${gtmId}"]`)
      noscripts.forEach(noscript => noscript.parentElement?.remove())
    }
  }, [gtmId])

  return null
}
