'use client'
import { usePathname } from 'next/navigation'
import { useEffect } from 'react'

export const BodyBackgroundSetter = () => {
  const pathname = usePathname()
  const isHomePage = pathname === '/'

  useEffect(() => {
    if (isHomePage) {
      document.body.classList.add('bg-light-liberty')
    } else {
      document.body.classList.remove('bg-light-liberty')
    }

    // Cleanup function to remove class when component unmounts
    return () => {
      document.body.classList.remove('bg-light-liberty')
    }
  }, [isHomePage])

  return null
}
