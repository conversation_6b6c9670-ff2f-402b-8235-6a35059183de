'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import React, { useEffect } from 'react'

import type { Page } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import { Media } from '@/components/Media'
import RichText from '@/components/RichText'
import { FormBlock } from '@/blocks/Form/Component'

export const HighImpactHero: React.FC<Page['hero'] & { enableForm?: boolean; form?: any }> = ({ links, media, richText, enableForm, form }) => {
  const { setHeaderTheme } = useHeaderTheme()

  useEffect(() => {
    setHeaderTheme('dark')
  })

  return (
    <div
      className="relative -mt-[110px] text-white"
      data-theme="light"
    >
      {/* Image container with fixed height */}
      <div className="relative h-[350px] md:h-[500px] lg:h-[600px] 2xl:h-[750px] before:absolute before:inset-0 before:bg-blue-gradient before:bg-blend-multiply before:z-10">
        {media && typeof media === 'object' && (
          <Media fill imgClassName="-z-10 object-cover" priority resource={media} />
        )}
      </div>

      {/* White container that overlaps the image by 100px */}
      <div className="relative -mt-[100px] md:-mt-[150px] lg:-mt-[200px] z-20 max-w-[1122px] mx-auto">
        <div className="mx-[26px] py-[20px] px-[30px] md:py-[30px] md:px-[40px] lg:py-[50px] lg:px-[60px] xl:py-[55px] bg-white">
          <div className="max-w-[36.5rem] md:max-w-none lg:grid lg:gap-[90px] lg:text-left mx-auto" style={{ gridTemplateColumns: '1fr 300px' }}>
            <div className="lg:col-span-1">
              {richText && <RichText className="mb-6" data={richText} enableGutter={false} />}
            </div>

            {/* Render form if enabled */}
            {enableForm && form && typeof form === 'object' && (
              <div className="lg:col-span-1">
                <FormBlock
                  form={form}
                  enableIntro={false}
                  blockType="formBlock"
                />
              </div>
            )}

            {/* Links span both columns */}
            {Array.isArray(links) && links.length > 0 && (
              <div className="md:col-span-2">
                <ul className="flex md:justify-center gap-4">
                  {links.map(({ link }, i) => {
                    return (
                      <li key={i}>
                        <CMSLink {...link} />
                      </li>
                    )
                  })}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
