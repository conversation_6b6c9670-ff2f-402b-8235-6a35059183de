import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { form, submissionData, actionKitPage } = body

    if (!form || !submissionData) {
      return NextResponse.json(
        { error: 'Form ID and submission data are required' },
        { status: 400 }
      )
    }

    const payload = await getPayload({ config })

    // If actionKitPage is provided, try Action Kit first
    if (actionKitPage) {
      try {
        // Convert submission data to Action Kit format
        const actionKitData: Record<string, any> = {
          page: actionKitPage
        }

        let mobilePhone: string | null = null

        // Add form fields to Action Kit data
        submissionData.forEach(({ field, value }: { field: string; value: any }) => {
          actionKitData[field] = value

          // Track mobile phone for SMS opt-in fields
          if (field === 'mobile_phone' && value) {
            mobilePhone = String(value)
          }
        })

        // If mobile phone is provided, add SMS opt-in fields
        if (mobilePhone) {
          actionKitData.user_sms_opt_in_number = mobilePhone
          actionKitData.action_add_mobile_phone_to_sms_number = mobilePhone
          actionKitData.user_sms_opt_in = true
          actionKitData.action_add_mobile_phone_to_sms_list = true
        }

        // Submit to Action Kit API
        const actionKitResponse = await fetch('https://act.elizabethwarren.com/rest/v1/action', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
          },
          body: JSON.stringify(actionKitData),
        })

        if (!actionKitResponse.ok) {
          const errorText = await actionKitResponse.text()
          console.error('Action Kit submission failed, saving to Payload:', errorText)

          // Action Kit failed, save to Payload as fallback
          const formSubmission = await payload.create({
            collection: 'form-submissions',
            data: {
              form,
              submissionData,
            },
          })

          return NextResponse.json({
            success: true,
            formSubmission,
            actionKit: {
              success: false,
              error: errorText,
              status: actionKitResponse.status
            }
          })
        }

        const actionKitResult = await actionKitResponse.json()

        // Action Kit successful, don't save to Payload
        return NextResponse.json({
          success: true,
          actionKit: {
            success: true,
            result: actionKitResult
          }
        })

      } catch (actionKitError) {
        console.error('Action Kit API error, saving to Payload:', actionKitError)

        // Action Kit failed, save to Payload as fallback
        const formSubmission = await payload.create({
          collection: 'form-submissions',
          data: {
            form,
            submissionData,
          },
        })

        return NextResponse.json({
          success: true,
          formSubmission,
          actionKit: {
            success: false,
            error: 'Action Kit submission failed'
          }
        })
      }
    }

    // No Action Kit integration, save to Payload
    const formSubmission = await payload.create({
      collection: 'form-submissions',
      data: {
        form,
        submissionData,
      },
    })

    return NextResponse.json({
      success: true,
      formSubmission
    })

  } catch (error) {
    console.error('Form submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
